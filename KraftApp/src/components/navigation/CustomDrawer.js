"use client"

import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  SafeAreaView,
  ScrollView,
  Dimensions
} from 'react-native';
import { colors } from '../../theme/colors';
import { spacing } from '../../theme/spacing';
import { textStyles } from '../../theme/typography';
import Icon from 'react-native-vector-icons/Feather';
import { useAuth } from '../../store/auth/AuthContext';

const { width } = Dimensions.get('window');

/**
 * Custom drawer menu component that doesn't rely on react-native-reanimated
 */
const CustomDrawer = ({ navigation, user }) => {
  const [isOpen, setIsOpen] = useState(false);

  const { logout } = useAuth();

  // Get user's initials for avatar
  const getInitials = () => {
    // If user has contactPerson property
    if (user?.contactPerson) {
      const names = user.contactPerson.split(" ");
      if (names.length > 1) {
        return `${names[0][0]}${names[1][0]}`.toUpperCase();
      }
      return names[0][0].toUpperCase();
    }

    // If user has email property
    if (user?.email) {
      const emailName = user.email.split('@')[0];
      if (emailName.length > 0) {
        return emailName[0].toUpperCase();
      }
    }

    // Default fallback
    return "?";
  };

  // Toggle drawer
  const toggleDrawer = () => {
    console.log('CustomDrawer: toggleDrawer called, current isOpen:', isOpen);
    setIsOpen(!isOpen);
  };

  // Navigate to a screen and close drawer
  const navigateTo = (screenName) => {
    setIsOpen(false);
    navigation.navigate(screenName);
  };

  // Handle logout
  const handleLogout = async () => {
    setIsOpen(false);
    await logout();
  };

  // Render hamburger button
  const renderHamburgerButton = () => (
    <TouchableOpacity style={styles.hamburgerButton} onPress={toggleDrawer}>
      <Icon name="menu" size={24} color={colors.white} />
    </TouchableOpacity>
  );

  // Render drawer content
  const renderDrawerContent = () => (
    <Modal
      visible={isOpen}
      transparent
      animationType="fade"
      onRequestClose={() => setIsOpen(false)}
    >
      <SafeAreaView style={styles.modalContainer}>
        <View style={styles.drawerContent}>
          <View style={styles.closeButtonContainer}>
            <TouchableOpacity onPress={() => setIsOpen(false)}>
              <Icon name="x" size={24} color={colors.textDark} />
            </TouchableOpacity>
          </View>

          <View style={styles.drawerHeader}>
            <View style={styles.avatar}>
              <Text style={styles.avatarText}>{getInitials()}</Text>
            </View>
            <Text style={styles.userName}>{user?.contactPerson || user?.email?.split('@')[0] || "User"}</Text>
            <Text style={styles.userCompany}>{user?.companyName || user?.email || "Company"}</Text>
          </View>

          <ScrollView style={styles.menuItems}>
          

            <TouchableOpacity style={styles.menuItem} onPress={() => navigateTo('ChangePassword')}>
              <Icon name="lock" size={20} color={colors.primary} />
              <Text style={styles.menuItemText}>Change Password</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.menuItem} onPress={() => navigateTo('Notifications')}>
              <Icon name="bell" size={20} color={colors.primary} />
              <Text style={styles.menuItemText}>Notifications</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.menuItem} onPress={() => navigateTo('PaymentTermUpgrade')}>
              <Icon name="dollar-sign" size={20} color={colors.primary} />
              <Text style={styles.menuItemText}>Payment Term Upgrade</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.menuItem} onPress={() => navigateTo('EditCompanyInformation')}>
              <Icon name="edit-2" size={20} color={colors.primary} />
              <Text style={styles.menuItemText}>Edit Company Information</Text>
            </TouchableOpacity>

         
            <View style={styles.divider} />

            <Text style={styles.sectionTitle}>Support</Text>
            <TouchableOpacity style={styles.menuItem} onPress={() => navigateTo('ContactSupport')}>
              <Icon name="help-circle" size={20} color={colors.primary} />
              <Text style={styles.menuItemText}>Contact Support</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.menuItem} onPress={() => navigateTo('FAQ')}>
              <Icon name="help-circle" size={20} color={colors.primary} />
              <Text style={styles.menuItemText}>FAQs</Text>
            </TouchableOpacity>

            <View style={styles.divider} />

            <Text style={styles.sectionTitle}>Legal</Text>
            <TouchableOpacity style={styles.menuItem} onPress={() => navigateTo('TermsAndConditions')}>
              <Icon name="file-text" size={20} color={colors.primary} />
              <Text style={styles.menuItemText}>Terms & Conditions</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.menuItem} onPress={() => navigateTo('PrivacyPolicy')}>
              <Icon name="shield" size={20} color={colors.primary} />
              <Text style={styles.menuItemText}>Privacy Policy</Text>
            </TouchableOpacity>

            <View style={styles.divider} />

            <TouchableOpacity style={[styles.menuItem, styles.logoutItem]} onPress={handleLogout}>
              <Icon name="log-out" size={20} color={colors.error} />
              <Text style={[styles.menuItemText, styles.logoutText]}>Logout</Text>
            </TouchableOpacity>
          </ScrollView>
        </View>
        <TouchableOpacity style={styles.overlay} onPress={() => setIsOpen(false)} />
      </SafeAreaView>
    </Modal>
  );

  return (
    <>
      {renderHamburgerButton()}
      {renderDrawerContent()}
    </>
  );
};

const styles = StyleSheet.create({
  hamburgerButton: {
    padding: spacing.small,
  },
  modalContainer: {
    flex: 1,
    flexDirection: 'row',
  },
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  drawerContent: {
    width: width * 0.75,
    maxWidth: 300,
    backgroundColor: colors.white,
    height: '100%',
    shadowColor: '#000',
    shadowOffset: { width: 2, height: 0 },
    shadowOpacity: 0.3,
    shadowRadius: 5,
    elevation: 5,
    transform: [{ translateX: 0 }],
    // Add animation properties
    animationName: 'slideIn',
    animationDuration: '0.3s',
    animationTimingFunction: 'ease-out',
  },
  closeButtonContainer: {
    alignItems: 'flex-end',
    padding: spacing.medium,
  },
  drawerHeader: {
    padding: spacing.medium,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
    alignItems: 'center',
    marginBottom: spacing.medium,
  },
  avatar: {
    width: 70,
    height: 70,
    borderRadius: 35,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.small,
  },
  avatarText: {
    ...textStyles.heading1,
    color: colors.white,
  },
  userName: {
    ...textStyles.heading3,
    color: colors.textDark,
    marginBottom: spacing.tiny,
  },
  userCompany: {
    ...textStyles.body2,
    color: colors.textLight,
  },
  menuItems: {
    flex: 1,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.medium,
    paddingHorizontal: spacing.large,
  },
  menuItemText: {
    ...textStyles.body1,
    color: colors.textDark,
    marginLeft: spacing.medium,
  },
  divider: {
    height: 1,
    backgroundColor: colors.border,
    marginVertical: spacing.small,
  },
  sectionTitle: {
    ...textStyles.body2,
    color: colors.textLight,
    marginLeft: spacing.large,
    marginTop: spacing.small,
    marginBottom: spacing.tiny,
  },
  logoutItem: {
    marginTop: spacing.medium,
  },
  logoutText: {
    color: colors.error,
  },
});

export default CustomDrawer;
